name: Build

on: 
  push:
    branches:
      - luals
    tags:
      - "*.*.*"

jobs:
  build:
    strategy:
      matrix:
        include:
         - {target: win32-x64,    file: emmylua_ls-win32-x64.zip}
         - {target: win32-arm64,  file: emmylua_ls-win32-arm64.zip}
         - {target: linux-x64,    file: emmylua_ls-linux-x64-glibc.2.17.tar.gz}
         - {target: linux-arm64,  file: emmylua_ls-linux-aarch64-glibc.2.17.tar.gz}
         - {target: darwin-x64,   file: emmylua_ls-darwin-x64.tar.gz}
         - {target: darwin-arm64, file: emmylua_ls-darwin-arm64.tar.gz}

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@master
    - name: Set node 20.x
      uses: actions/setup-node@v3
      with:
          node-version: 20.x
    - name: Install
      uses: borales/actions-yarn@v5
      with: 
        cmd: install
      env:
       GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    - name: build
      run: |
        node ./build/prepare.js ${{ matrix.file }}
        npx vsce package -o VSCode-EmmyLua-Luals-${{ matrix.target }}.vsix --target ${{ matrix.target }}
    - name: Upload
      uses: actions/upload-artifact@v4
      with: 
        name: VSCode-EmmyLua-Luals-${{ matrix.target }}
        path: ${{ github.workspace }}/VSCode-EmmyLua-Luals*.vsix
  
  publish:
    runs-on: ubuntu-latest
    needs: [build]
    if: success() && startsWith(github.ref, 'refs/tags/')
    steps:
      - uses: actions/download-artifact@v4
      - run: npx vsce publish --packagePath $(find VSCode-EmmyLua-Luals* -iname "*.vsix")
        env:
          VSCE_PAT: ${{ secrets.VSCE_ACCESS_TOKEN }}
