{
	"comments": {
		"lineComment": "--",
		"blockComment": ["--[[", "]]"]
	},
	"brackets": [
		["[", "]"],
		["(", ")"],
		["{", "}"]
	],
	"autoClosingPairs": [
		{ "open": "[", "close": "]" },
		{ "open": "(", "close": ")" },
		{ "open": "{", "close": "}", "notIn": ["string", "comment"] },
		{ "open": "'", "close": "'", "notIn": ["string", "comment"] },
		{ "open": "\"", "close": "\"", "notIn": ["string"] }
	],
	"surroundingPairs": [
		["[", "]"],
		["(", ")"],
		["{", "}"],
		["\"", "\""],
		["'", "'"]
	],
	"folding": {
		"markers": {
			"start": "^\\s*--region\\b",
			"end": "^\\s*--endregion\\b"
		}
	},
	"indentationRules": {
		"increaseIndentPattern": "\\b(do|else|then|repeat|function)\\b((?!end).)*$",
		// "decreaseIndentPattern": "\\b(end|else|elseif|until)\\b"
	}
}
