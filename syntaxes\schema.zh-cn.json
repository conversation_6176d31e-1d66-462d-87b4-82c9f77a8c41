{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Emmyrc", "type": "object", "properties": {"$schema": {"type": ["string", "null"]}, "codeAction": {"default": {"insertSpace": false}, "allOf": [{"$ref": "#/definitions/EmmyrcCodeAction"}]}, "codeLens": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcCodeLen"}]}, "completion": {"default": {"autoRequire": true, "autoRequireFunction": "require", "autoRequireNamingConvention": "keep", "autoRequireSeparator": ".", "baseFunctionIncludesName": true, "callSnippet": false, "enable": true, "postfix": "@"}, "allOf": [{"$ref": "#/definitions/EmmyrcCompletion"}]}, "diagnostics": {"default": {"diagnosticInterval": 500, "disable": [], "enable": true, "enables": [], "globals": [], "globalsRegex": [], "severity": {}}, "allOf": [{"$ref": "#/definitions/EmmyrcDiagnostic"}]}, "documentColor": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcDocumentColor"}]}, "hint": {"default": {"enable": true, "indexHint": true, "localHint": true, "metaCallHint": true, "overrideHint": true, "paramHint": true}, "allOf": [{"$ref": "#/definitions/EmmyrcInlayHint"}]}, "hover": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcHover"}]}, "references": {"default": {"enable": true, "fuzzySearch": true, "shortStringSearch": false}, "allOf": [{"$ref": "#/definitions/EmmyrcReference"}]}, "resource": {"default": {"paths": []}, "allOf": [{"$ref": "#/definitions/EmmyrcResource"}]}, "runtime": {"default": {"classDefaultCall": {"forceNonColon": false, "forceReturnSelf": false, "functionName": ""}, "extensions": [], "frameworkVersions": [], "requireLikeFunction": [], "requirePattern": [], "version": "LuaLatest"}, "allOf": [{"$ref": "#/definitions/EmmyrcRuntime"}]}, "semanticTokens": {"default": {"enable": true}, "allOf": [{"$ref": "#/definitions/EmmyrcSemanticToken"}]}, "signature": {"default": {"detailSignatureHelper": true}, "allOf": [{"$ref": "#/definitions/EmmyrcSignature"}]}, "strict": {"default": {"arrayIndex": true, "docBaseConstMatchBaseType": true, "metaOverrideFileDefine": true, "requirePath": false, "typeCall": false}, "allOf": [{"$ref": "#/definitions/EmmyrcStrict"}]}, "workspace": {"default": {"enableReindex": false, "encoding": "utf-8", "ignoreDir": [], "ignoreGlobs": [], "library": [], "moduleMap": [], "preloadFileSize": 0, "reindexDuration": 5000, "workspaceRoots": []}, "allOf": [{"$ref": "#/definitions/EmmyrcWorkspace"}]}}, "definitions": {"ClassDefaultCall": {"type": "object", "properties": {"forceNonColon": {"description": "强制非冒号定义。当 `function_name` 不为空时，生效。", "default": true, "type": "boolean"}, "forceReturnSelf": {"description": "强制返回 `self`。", "default": true, "type": "boolean"}, "functionName": {"description": "类默认重载函数。例如：\"__init\"。", "default": "", "type": "string"}}}, "DiagnosticCode": {"oneOf": [{"type": "string", "enum": ["none"]}, {"description": "语法错误。", "type": "string", "enum": ["syntax-error"]}, {"description": "注解语法错误。", "type": "string", "enum": ["doc-syntax-error"]}, {"description": "类型未找到。", "type": "string", "enum": ["type-not-found"]}, {"description": "缺少返回语句。", "type": "string", "enum": ["missing-return"]}, {"description": "参数类型不匹配。", "type": "string", "enum": ["param-type-not-match"]}, {"description": "缺少参数。", "type": "string", "enum": ["missing-parameter"]}, {"description": "冗余参数。", "type": "string", "enum": ["redundant-parameter"]}, {"description": "不可达的代码。", "type": "string", "enum": ["unreachable-code"]}, {"description": "未使用的变量。", "type": "string", "enum": ["unused"]}, {"description": "未定义的全局变量。", "type": "string", "enum": ["undefined-global"]}, {"description": "已弃用。", "type": "string", "enum": ["deprecated"]}, {"description": "成员可见性检查。", "type": "string", "enum": ["access-invisible"]}, {"description": "不可丢弃返回值。", "type": "string", "enum": ["discard-returns"]}, {"description": "未定义的字段。", "type": "string", "enum": ["undefined-field"]}, {"description": "局部常量重新赋值。", "type": "string", "enum": ["local-const-reassign"]}, {"description": "迭代变量重新赋值。", "type": "string", "enum": ["iter-variable-reassign"]}, {"description": "重复定义的类型。", "type": "string", "enum": ["duplicate-type"]}, {"description": "重复定义的局部变量。", "type": "string", "enum": ["redefined-local"]}, {"description": "重复定义的标签。", "type": "string", "enum": ["redefined-label"]}, {"description": "代码风格检查。", "type": "string", "enum": ["code-style-check"]}, {"description": "需要检查 nil。", "type": "string", "enum": ["need-check-nil"]}, {"description": "在同步上下文中使用 await。", "type": "string", "enum": ["await-in-sync"]}, {"description": "注解使用错误。", "type": "string", "enum": ["annotation-usage-error"]}, {"description": "返回类型不匹配。", "type": "string", "enum": ["return-type-mismatch"]}, {"description": "缺少返回值。", "type": "string", "enum": ["missing-return-value"]}, {"description": "冗余的返回值。", "type": "string", "enum": ["redundant-return-value"]}, {"description": "未使用注解定义的参数。", "type": "string", "enum": ["undefined-doc-param"]}, {"description": "重复定义的字段(注解)。", "type": "string", "enum": ["duplicate-doc-field"]}, {"description": "缺少字段。", "type": "string", "enum": ["missing-fields"]}, {"description": "注入字段。", "type": "string", "enum": ["inject-field"]}, {"description": "类型循环依赖。", "type": "string", "enum": ["circle-doc-class"]}, {"description": "不完整的签名文档。", "type": "string", "enum": ["incomplete-signature-doc"]}, {"description": "全局变量缺少文档。", "type": "string", "enum": ["missing-global-doc"]}, {"description": "赋值类型不匹配。", "type": "string", "enum": ["assign-type-mismatch"]}, {"description": "重复的 require 导入。", "type": "string", "enum": ["duplicate-require"]}, {"description": "在 assert 中使用了非字面量表达式。", "type": "string", "enum": ["non-literal-expressions-in-assert"]}, {"description": "不平衡的赋值。", "type": "string", "enum": ["unbalanced-assignments"]}, {"description": "不必要的 assert。", "type": "string", "enum": ["unnecessary-assert"]}, {"description": "不必要的 if。", "type": "string", "enum": ["unnecessary-if"]}, {"description": "重复定义的字段(实际代码)。", "type": "string", "enum": ["duplicate-set-field"]}, {"description": "重复的索引。", "type": "string", "enum": ["duplicate-index"]}, {"description": "泛型约束不匹配。", "type": "string", "enum": ["generic-constraint-mismatch"]}]}, "DiagnosticSeveritySetting": {"oneOf": [{"description": "诊断严重性: 错误。", "type": "string", "enum": ["error"]}, {"description": "诊断严重性: 警告。", "type": "string", "enum": ["warning"]}, {"description": "诊断严重性: 信息。", "type": "string", "enum": ["information"]}, {"description": "诊断严重性: 提示。", "type": "string", "enum": ["hint"]}]}, "EmmyrcCodeAction": {"type": "object", "properties": {"insertSpace": {"description": "是否在 '---' 后插入空格。", "default": false, "type": "boolean"}}}, "EmmyrcCodeLen": {"type": "object", "properties": {"enable": {"description": "是否启用代码透镜(Code Lens)。", "default": true, "type": "boolean"}}}, "EmmyrcCompletion": {"description": "EmmyLua 代码补全配置。", "type": "object", "properties": {"autoRequire": {"description": "自动补全 require 语句。", "default": true, "type": "boolean"}, "autoRequireFunction": {"description": "自动补全 require 语句时使用的函数名。", "default": "require", "type": "string"}, "autoRequireNamingConvention": {"description": "自动补全 require 语句时使用的命名规范。", "default": "keep", "allOf": [{"$ref": "#/definitions/EmmyrcFilenameConvention"}]}, "autoRequireSeparator": {"description": "自动补全 require 语句时使用的分隔符。", "default": ".", "type": "string"}, "baseFunctionIncludesName": {"description": "是否在基本函数补全中包含函数名。效果: `function () end` -> `function name() end`。", "default": true, "type": "boolean"}, "callSnippet": {"description": "是否使用代码片段补全函数调用。", "default": false, "type": "boolean"}, "enable": {"description": "是否启用代码补全。", "default": true, "type": "boolean"}, "postfix": {"description": "后缀补全触发关键词。", "default": "@", "type": "string"}}}, "EmmyrcDiagnostic": {"description": "Emmyrc 诊断配置。", "type": "object", "properties": {"diagnosticInterval": {"description": "诊断间隔时间(毫秒)。", "type": ["integer", "null"], "format": "uint64", "minimum": 0}, "disable": {"description": "禁用的诊断代码列表。", "default": [], "type": "array", "items": {"$ref": "#/definitions/DiagnosticCode"}}, "enable": {"description": "是否启用诊断。", "default": true, "type": "boolean"}, "enables": {"description": "启用的诊断代码列表。", "default": [], "type": "array", "items": {"$ref": "#/definitions/DiagnosticCode"}}, "globals": {"description": "全局变量列表，在该列表中的全局变量不会被诊断为未定义。", "default": [], "type": "array", "items": {"type": "string"}}, "globalsRegex": {"description": "全局变量正则表达式列表，符合正则表达式的全局变量不会被诊断为未定义。", "default": [], "type": "array", "items": {"type": "string"}}, "severity": {"description": "诊断代码与严重性设置的映射。", "default": {}, "type": "object", "additionalProperties": {"$ref": "#/definitions/DiagnosticSeveritySetting"}}}}, "EmmyrcDocumentColor": {"type": "object", "properties": {"enable": {"description": "是否启用文档颜色。", "default": true, "type": "boolean"}}}, "EmmyrcFilenameConvention": {"oneOf": [{"description": "保持原始文件名。", "type": "string", "enum": ["keep"]}, {"description": "将文件名转换为蛇形(snake_case)命名。", "type": "string", "enum": ["snake-case"]}, {"description": "将文件名转换为帕斯卡(PascalCase)命名。", "type": "string", "enum": ["pascal-case"]}, {"description": "将文件名转换为驼峰(camelCase)命名。", "type": "string", "enum": ["camel-case"]}]}, "EmmyrcHover": {"type": "object", "properties": {"enable": {"description": "是否启用悬浮提示。", "default": true, "type": "boolean"}}}, "EmmyrcInlayHint": {"type": "object", "properties": {"enable": {"description": "是否启用内联提示。", "default": true, "type": "boolean"}, "indexHint": {"description": "在索引表达式跨行时，显示提示。", "default": true, "type": "boolean"}, "localHint": {"description": "是否启用局部变量提示。", "default": true, "type": "boolean"}, "metaCallHint": {"description": "是否启用 `__call` 调用提示。", "default": true, "type": "boolean"}, "overrideHint": {"description": "是否启用重写提示。", "default": true, "type": "boolean"}, "paramHint": {"description": "是否启用参数提示。", "default": true, "type": "boolean"}}}, "EmmyrcLuaVersion": {"oneOf": [{"description": "Lua 5.1", "type": "string", "enum": ["Lua5.1"]}, {"description": "LuaJIT", "type": "string", "enum": ["LuaJIT"]}, {"description": "Lua 5.2", "type": "string", "enum": ["Lua5.2"]}, {"description": "Lua 5.3", "type": "string", "enum": ["Lua5.3"]}, {"description": "Lua 5.4", "type": "string", "enum": ["Lua5.4"]}, {"description": "Lua 5.5", "type": "string", "enum": ["Lua5.5"]}, {"description": "最新版本的 Lua。", "type": "string", "enum": ["LuaLatest"]}]}, "EmmyrcReference": {"type": "object", "properties": {"enable": {"description": "是否启用引用搜索。", "default": true, "type": "boolean"}, "fuzzySearch": {"description": "是否启用模糊搜索。", "default": true, "type": "boolean"}, "shortStringSearch": {"description": "缓存短字符串用于搜索。", "default": false, "type": "boolean"}}}, "EmmyrcResource": {"type": "object", "properties": {"paths": {"default": [], "type": "array", "items": {"type": "string"}}}}, "EmmyrcRuntime": {"type": "object", "properties": {"classDefaultCall": {"description": "类默认重载函数。", "default": {"forceNonColon": false, "forceReturnSelf": false, "functionName": ""}, "allOf": [{"$ref": "#/definitions/ClassDefaultCall"}]}, "extensions": {"description": "文件扩展名。例如：.lua, .lua.txt", "default": [], "type": "array", "items": {"type": "string"}}, "frameworkVersions": {"description": "框架版本列表。", "default": [], "type": "array", "items": {"type": "string"}}, "requireLikeFunction": {"description": "类似 require 的函数列表。", "default": [], "type": "array", "items": {"type": "string"}}, "requirePattern": {"description": "require 模式。例如：\"?.lua\", \"?/init.lua\"", "default": [], "type": "array", "items": {"type": "string"}}, "version": {"description": "<PERSON><PERSON> 版本。", "default": "LuaLatest", "allOf": [{"$ref": "#/definitions/EmmyrcLuaVersion"}]}}}, "EmmyrcSemanticToken": {"type": "object", "properties": {"enable": {"description": "是否启用语义标记。", "default": true, "type": "boolean"}}}, "EmmyrcSignature": {"type": "object", "properties": {"detailSignatureHelper": {"description": "是否启用签名帮助。", "default": true, "type": "boolean"}}}, "EmmyrcStrict": {"type": "object", "properties": {"arrayIndex": {"description": "是否启用严格模式数组索引，严格模式下数组取值返回将包含 nil。", "default": true, "type": "boolean"}, "docBaseConstMatchBaseType": {"description": "doc定义的基础常量类型可以匹配基础类型，使 int 可以匹配 `---@alias id 1|2|3`，string 同理。", "default": false, "type": "boolean"}, "metaOverrideFileDefine": {"description": "`---@meta`文件的定义完全覆盖真实文件的定义。", "default": true, "type": "boolean"}, "requirePath": {"description": "是否启用严格模式 require 路径。严格模式时 require 必须从指定的根目录开始。", "default": false, "type": "boolean"}, "typeCall": {"default": false, "type": "boolean"}}}, "EmmyrcWorkspace": {"type": "object", "properties": {"enableReindex": {"description": "启用重新索引。", "default": false, "type": "boolean"}, "encoding": {"description": "编码。例如：\"utf-8\"", "default": "utf-8", "type": "string"}, "ignoreDir": {"description": "忽略的目录。", "default": [], "type": "array", "items": {"type": "string"}}, "ignoreGlobs": {"description": "忽略的文件。例如：[\"**/*.lua\"]", "default": [], "type": "array", "items": {"type": "string"}}, "library": {"description": "库路径。例如：\"/usr/local/share/lua/5.1\"", "default": [], "type": "array", "items": {"type": "string"}}, "moduleMap": {"description": "模块映射列表。key 是正则表达式，value 是新的模块正则表达式。例如：{ \"^(.*)$\": \"module_$1\" \"^lib(.*)$\": \"script$1\" }", "default": [], "type": "array", "items": {"$ref": "#/definitions/EmmyrcWorkspaceModuleMap"}}, "preloadFileSize": {"default": 0, "type": "integer", "format": "int32"}, "reindexDuration": {"description": "当保存文件时，ls 将在指定毫秒后重新索引工作区。", "default": 5000, "type": "integer", "format": "uint64", "minimum": 0}, "workspaceRoots": {"description": "工作区根目录列表。例如：[\"src\", \"test\"]", "default": [], "type": "array", "items": {"type": "string"}}}}, "EmmyrcWorkspaceModuleMap": {"type": "object", "required": ["pattern", "replace"], "properties": {"pattern": {"type": "string"}, "replace": {"type": "string"}}}}}