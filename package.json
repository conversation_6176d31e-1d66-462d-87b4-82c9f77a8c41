{"displayName": "em<PERSON><PERSON><PERSON>-l<PERSON>s", "name": "em<PERSON><PERSON><PERSON>-l<PERSON>s", "description": "Emmy<PERSON>ua with LuaLS-oriented behavior", "version": "0.9.22", "icon": "res/icon.png", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "engines": {"vscode": "^1.89.0"}, "categories": ["Linters", "Programming Languages", "Snippets"], "activationEvents": [], "keywords": ["<PERSON><PERSON>", "IntelliSense", "<PERSON><PERSON><PERSON>"], "repository": {"type": "git", "url": "https://github.com/xuhuanzy/VSCode-EmmyLua-Luals"}, "bugs": {"url": "https://github.com/EmmyLua/VSCode-EmmyLua/issues"}, "main": "./out/extension", "contributes": {"commands": [{"command": "emmy.restartServer", "title": "EmmyLua: <PERSON><PERSON>"}, {"command": "emmy.stopServer", "title": "EmmyLua: Stop EmmyLua Language Server"}], "languages": [{"id": "lua", "extensions": [".lua"], "aliases": ["<PERSON><PERSON><PERSON>"], "configuration": "./language-configuration.json"}], "breakpoints": [{"language": "lua"}], "jsonValidation": [{"fileMatch": ".emmyrc.json", "url": "emmyrc-schema://schemas/emmyrc"}], "configuration": {"title": "<PERSON><PERSON><PERSON>", "properties": {"emmylua.colors.mutable_underline": {"type": "boolean", "default": true, "description": "Mutable variable underline"}, "emmylua.develop.port": {"type": "number", "default": 5007, "description": "%config.develop.port%"}, "emmylua.ls.startParameters": {"type": "array", "default": []}, "emmylua.language.completeAnnotation": {"type": "boolean", "default": true, "scope": "resource", "description": "%config.language.completeAnnotation%"}, "emmylua.misc.executablePath": {"type": "string", "scope": "resource", "default": "", "description": "%config.misc.executablePath%"}, "emmylua.misc.globalConfigPath": {"type": "string", "default": "", "description": "%config.misc.globalConfigPath%"}, "lua.trace.server": {"type": "string", "enum": ["off", "messages", "verbose"], "default": "off", "markdownDescription": "Traces the communication between VS Code and the EmmyLua language server in the Output view. <PERSON><PERSON><PERSON> is `off`.", "scope": "window"}}}, "configurationDefaults": {"[lua]": {"editor.quickSuggestions": {"other": true, "comments": true, "strings": true}}}, "colors": [], "semanticTokenScopes": [{"language": "lua", "scopes": {"class": ["support.class.lua"], "class.declaration": ["support.class.lua"], "comment.documentation": ["storage.type.annotation.lua"], "event.static": ["support.class.lua"], "function": ["support.function.any-method.lua"], "function.declaration": ["entity.name.function.lua"], "function.defaultLibrary": ["support.function.lua"], "function.static": ["entity.name.function.lua"], "keyword": ["keyword.control.lua"], "keyword.async": ["entity.name.tag.lua"], "keyword.declaration": ["keyword.local.lua"], "keyword.documentation": ["storage.type.annotation.lua"], "keyword.readonly": ["constant.language.lua"], "macro": ["variable.lua"], "method": ["entity.name.function.lua"], "method.declaration": ["entity.name.function.lua"], "number": ["constant.numeric.float.lua"], "number.static": ["constant.numeric.integer.lua"], "operator": ["keyword.operator.lua"], "parameter.declaration": ["variable.parameter.lua"], "property": ["entity.other.attribute.lua"], "property.declaration": ["entity.other.property.lua"], "string": ["string.lua"], "string.deprecated": ["invalid.illegal.character.escape.lua"], "string.modification": ["constant.character.escape.lua"], "struct": ["string.tag.lua"], "struct.declaration": ["string.tag.lua"], "type": ["support.type.lua"], "type.modification": ["storage.type.generic.lua"], "type.readonly": ["storage.type.self.lua"], "typeParameter": ["string.tag.lua"], "variable": ["variable.other.lua"], "variable.abstract": ["variable.other.constant.lua"], "variable.declaration": ["variable.other.lua"], "variable.defaultLibrary": ["support.constant.lua"], "variable.definition": ["variable.language.self.lua"], "variable.global": ["variable.global.lua"], "variable.readonly": ["variable.other.constant.lua"]}}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "test": "npm run compile && node ./node_modules/vscode/bin/test"}, "devDependencies": {"@types/mocha": "^9.1.0", "@types/node": "^17.0.21", "@types/vscode": "1.89.0", "@vscode/vsce": "2.26.1", "download": "^7.1.0", "eslint": "^8.11.0", "filecopy": "^4.0.2", "typescript": "^4.0.2", "decompress-targz": "4.1.1"}, "dependencies": {"@vscode/debugadapter": "^1.61.0", "@vscode/debugprotocol": "^1.61.0", "iconv-lite": "^0.6.3", "smart-buffer": "^4.0.1", "vscode-languageclient": "9.0.1", "concat-map": "0.0.2"}}