// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
    "configurations": [
    
        {
            "name": "Extension",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}",
                "${workspaceFolder}/../lua-analyzer-test"  // 指定要打开的文件路径
                // "${workspaceFolder}/../lua-analyzer-fix"
                // "D:\\Workspace\\game\\y3\\y3-lualib\\"
            ],
            "outFiles": [
                "${workspaceFolder}/out/**/*.js"
            ],
            "env": {
                "EMMY_DEV": "true"
            },
            "preLaunchTask": "npm: watch"
        },
        {
            "name": "Extension Tests",
            "type": "extensionHost",
            "request": "launch",
            "runtimeExecutable": "${execPath}",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}",
                "--extensionTestsPath=${workspaceFolder}/out/test"
            ],
            "outFiles": [
                "${workspaceFolder}/out/test/**/*.js"
            ],
            "preLaunchTask": "npm: watch"
        }
    ]
}
